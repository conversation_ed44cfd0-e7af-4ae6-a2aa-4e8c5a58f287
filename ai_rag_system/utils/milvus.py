import os
import logging
import time
from pymilvus import MilvusClient

# 设置日志
logger = logging.getLogger(__name__)

def create_milvus_client(max_retries=3, retry_delay=1):
    """
    创建 Milvus 客户端，包含重试机制
    
    Args:
        max_retries (int): 最大重试次数
        retry_delay (int): 重试间隔（秒）
        
    Returns:
        MilvusClient: Milvus 客户端实例
    """
    milvus_uri = os.getenv('MILVUS_URI', 'http://localhost:19530')
    
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试连接到 Milvus: {milvus_uri} (尝试 {attempt + 1}/{max_retries})")
            
            # 创建 Milvus 客户端，增加超时设置
            client = MilvusClient(
                uri=milvus_uri,
                timeout=10
            )
            
            # 测试连接
            client._get_connection()
            logger.info("成功连接到 Milvus")
            return client
            
        except Exception as e:
            logger.error(f"连接 Milvus 失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                # 最后一次尝试失败，使用本地默认配置作为后备
                logger.warning("使用本地默认配置作为后备方案")
                return MilvusClient(uri="http://localhost:19530")

# 创建客户端实例
client = create_milvus_client()

def list_collections():
    """列出所有集合"""
    return client.list_collections()

def drop_collection(collection_name):
    """
    删除指定集合
    
    Args:
        collection_name (str): 集合名称
    """
    return client.drop_collection(collection_name=collection_name)